/*
 * L25编程语言编译器和虚拟机实现
 *
 * 这是一个完整的L25语言实现，包含：
 * - 词法分析器（Lexer）：将源代码转换为标记流
 * - 语法分析器（Parser）：将标记流转换为虚拟机指令
 * - 虚拟机（VM）：执行生成的指令
 *
 * L25语言特性：
 * - 基本数据类型：整数
 * - 控制结构：if-else、while循环
 * - 函数定义和调用
 * - 一维静态数组
 * - 结构体定义和访问
 * - 输入输出操作
 */

#include <fcntl.h>      // 文件操作相关函数
#include <memory.h>     // 内存操作函数（如memcmp）
#include <stdint.h>     // 标准整数类型定义
#include <stdio.h>      // 标准输入输出函数
#include <stdlib.h>     // 标准库函数（如malloc、exit）
#include <string.h>     // 字符串操作函数
#include <unistd.h>     // UNIX标准函数（如read、write）

/*
 * L25语言内部使用的64位整数类型定义
 * 使用特定的typedef比全局#define int更安全和可移植
 */
typedef int64_t l25_int;

/* ========== 全局变量定义 ========== */
int MAX_SIZE; // 内存段的最大字节大小

/* 虚拟机内存段 */
l25_int *code;       // 代码段：存储编译后的虚拟机指令
l25_int *code_dump;  // 代码段备份：用于调试输出
l25_int *stack;      // 栈段：运行时数据栈

/* 边界检查指针 */
l25_int *code_end;    // 代码段结束位置
l25_int *stack_start; // 栈段起始位置

/* 虚拟机寄存器 */
l25_int *pc; // 程序计数器：指向当前执行的指令
l25_int *sp; // 栈指针：指向栈顶
l25_int *bp; // 基址指针：指向当前栈帧的基址

l25_int ax;    // 通用寄存器：用于计算和临时数据存储
l25_int cycle; // 指令执行计数器

/* 词法分析器和语法分析器全局状态 */
char *src;      // 源代码指针：指向当前正在分析的字符
char *src_dump; // 源代码备份：源代码的原始起始地址

l25_int *symbol_table;      // 符号表：存储所有标识符的信息
l25_int *symbol_ptr;        // 符号表指针：指向当前符号表条目
l25_int *symbol_table_end;  // 符号表边界指针：用于溢出检查

l25_int token;     // 当前标记类型
l25_int token_val; // 当前标记值（如果是数字）
int line;          // 当前行号：用于错误报告（标准int足够）

/* L25语言特定的解析器和虚拟机状态 */
l25_int *entry_main_addr = 0;       // main()函数入口地址
l25_int current_func_var_count = 0; // 当前函数中的局部变量数量
int current_parsing_func_body = 0;  // 标志：1表示正在解析函数体，0表示否

/* 函数参数解析 */
#define MAX_PARAMS 10
l25_int *param_sym_entries[MAX_PARAMS]; // 存储函数参数的符号条目

/* 结构体定义 */
#define MAX_STRUCT_MEMBERS 20  // 每个结构体最大成员数
#define MAX_STRUCTS 10         // 最大结构体定义数
l25_int *struct_members[MAX_STRUCTS][MAX_STRUCT_MEMBERS]; // 存储每个结构体的成员符号条目
int struct_count = 0;                                     // 已定义的结构体数量

/* 基本常量 */
#define MAX_ARRAY_SIZE 10000 // 最大数组大小

/* ========== 枚举定义 ========== */

/*
 * 虚拟机指令集
 * 定义了L25虚拟机支持的所有指令类型
 */
enum {
    // 基本数据操作
    IMM,  // 立即数加载：将常量值加载到ax寄存器
    LEA,  // 加载有效地址：计算变量的内存地址
    JMP,  // 无条件跳转：跳转到指定地址
    JZ,   // 零跳转：如果ax为0则跳转
    JNZ,  // 非零跳转：如果ax非0则跳转

    // 函数调用相关
    CALL, // 函数调用：调用指定地址的函数
    NVAR, // 新建变量空间：为局部变量分配栈空间
    DARG, // 删除参数：清理函数参数占用的栈空间
    RET,  // 函数返回：从函数返回到调用点

    // 内存访问
    LI,   // 加载间接：从ax指向的地址加载值到ax
    SI,   // 存储间接：将ax的值存储到栈顶地址指向的位置

    // 栈操作
    PUSH, // 压栈：将ax的值压入栈

    // 算术运算
    ADD,  // 加法：栈顶值 + ax
    SUB,  // 减法：栈顶值 - ax
    MUL,  // 乘法：栈顶值 * ax
    DIV,  // 除法：栈顶值 / ax

    // 比较运算
    EQ,   // 等于：栈顶值 == ax
    NE,   // 不等于：栈顶值 != ax
    LT,   // 小于：栈顶值 < ax
    GT,   // 大于：栈顶值 > ax
    LE,   // 小于等于：栈顶值 <= ax
    GE,   // 大于等于：栈顶值 >= ax

    // 程序控制
    EXIT, // 程序退出：终止程序执行

    // 输入输出
    READ_INT_AX,  // 读取整数到ax寄存器
    PRINT_INT_AX, // 打印ax寄存器中的整数

    // 数组和结构体访问
    LEA_ARRAY,  // 数组元素地址计算：计算数组元素的地址
    LEA_STRUCT  // 结构体成员地址计算：计算结构体成员的地址
};

/*
 * L25语言标记类型
 * 定义了词法分析器识别的所有标记类型
 */
enum {
    // 基本标记（从128开始避免与ASCII字符冲突）
    Num = 128,    // 数字字面量
    Fun,          // 函数标识符
    Loc,          // 局部变量标识符
    Struct,       // 结构体标识符
    Array,        // 数组标识符
    Id,           // 通用标识符

    // 关键字
    Program,      // program关键字
    FuncKw,       // func关键字
    MainKw,       // main关键字
    Let,          // let关键字（变量声明）
    If,           // if关键字
    Else,         // else关键字
    Return,       // return关键字
    While,        // while关键字
    Input,        // input关键字（输入操作）
    Output,       // output关键字（输出操作）
    StructKw,     // struct关键字

    // 运算符
    Assign,       // 赋值运算符 =
    Add,          // 加法运算符 +
    Sub,          // 减法运算符 -
    Mul,          // 乘法运算符 *
    Div,          // 除法运算符 /
    Eq,           // 等于运算符 ==
    Ne,           // 不等于运算符 !=
    Lt,           // 小于运算符 <
    Gt,           // 大于运算符 >
    Le,           // 小于等于运算符 <=
    Ge,           // 大于等于运算符 >=

    // 特殊符号
    Dot,          // 点号 .（结构体成员访问）
    Colon         // 冒号 :（类型声明）
};

/*
 * 符号表条目字段
 * 定义了符号表中每个条目包含的字段
 */
enum {
    Token,    // 标记类型
    Hash,     // 哈希值（用于快速查找）
    Name,     // 名称指针
    Class,    // 符号类别（函数、变量等）
    Type,     // 数据类型
    Value,    // 值或地址
    Extra,    // 额外信息（如数组大小、结构体成员数等）
    SymSize   // 符号表条目大小（字段数量）
};

/*
 * L25支持的数据类型
 */
enum {
    INT_TYPE,    // 整数类型
    STRUCT_TYPE, // 结构体类型
    ARRAY_TYPE   // 数组类型
};

/* ========== 词法分析器（标记化器）========== */

/*
 * 词法分析函数
 * 功能：将源代码字符流转换为标记流
 *
 * 处理的标记类型：
 * - 标识符和关键字
 * - 数字字面量
 * - 运算符和比较符
 * - 特殊符号（括号、分号等）
 * - 注释（//开头的行注释）
 */
void tokenize() {
    char *ch_ptr;              // 字符指针：标记当前标识符的起始位置
    l25_int current_hash_val;  // 当前哈希值：用于标识符的快速查找

    // 主循环：逐字符扫描源代码
    while ((token = *src++)) {
        if (token == '\n') {
            line++; // 遇到换行符，行号递增
        }
        else if (token == ' ' || token == '\t' || token == '\r') {
            // 跳过空白字符（空格、制表符、回车符）
        }
        else if ((token >= 'a' && token <= 'z') || (token >= 'A' && token <= 'Z') || (token == '_')) {
            // 处理标识符和关键字（以字母或下划线开头）
            ch_ptr = src - 1;           // 记录标识符起始位置
            current_hash_val = token;   // 初始化哈希值

            // 继续读取标识符的其余部分（字母、数字、下划线）
            while ((*src >= 'a' && *src <= 'z') || (*src >= 'A' && *src <= 'Z') ||
                   (*src >= '0' && *src <= '9') || (*src == '_')) {
                current_hash_val = current_hash_val * 147 + *src++; // 计算哈希值
            }
            current_hash_val = (current_hash_val << 6) + (src - ch_ptr); // 加入长度信息

            // 在符号表中查找该标识符
            symbol_ptr = symbol_table;
            while (symbol_ptr[Token]) {
                // 比较哈希值和字符串内容
                if (current_hash_val == symbol_ptr[Hash] &&
                    !memcmp((char *)symbol_ptr[Name], ch_ptr, src - ch_ptr)) {
                    token = symbol_ptr[Token]; // 找到已存在的符号
                    return;
                }
                symbol_ptr = symbol_ptr + SymSize; // 移动到下一个符号表条目
            }

            // 如果是新标识符，添加到符号表
            symbol_ptr[Name] = (l25_int)(uintptr_t)ch_ptr; // 存储名称指针
            symbol_ptr[Hash] = current_hash_val;           // 存储哈希值
            token = symbol_ptr[Token] = Id;                // 设置为标识符类型
            return;
        }
        else if (token >= '0' && token <= '9') {
            // 处理数字字面量
            token_val = token - '0'; // 初始化数字值
            while (*src >= '0' && *src <= '9') {
                token_val = token_val * 10 + *src++ - '0'; // 构建完整的数字
            }
            token = Num; // 设置标记类型为数字
            return;
        }
        else if (token == '/') {
            // 处理除法运算符或注释
            if (*src == '/') {
                // 行注释：跳过到行尾
                while (*src != 0 && *src != '\n') src++;
            } else {
                token = Div; // 除法运算符
                return;
            }
        }
        // 处理双字符运算符
        else if (token == '=') {
            if (*src == '=') { src++; token = Eq; }     // ==
            else { token = Assign; }                    // =
            return;
        }
        else if (token == '!') {
            if (*src == '=') { src++; token = Ne; }     // !=
            return;
        }
        else if (token == '<') {
            if (*src == '=') { src++; token = Le; }     // <=
            else { token = Lt; }                        // <
            return;
        }
        else if (token == '>') {
            if (*src == '=') { src++; token = Ge; }     // >=
            else { token = Gt; }                        // >
            return;
        }
        // 处理单字符运算符
        else if (token == '+') { token = Add; return; }    // +
        else if (token == '-') { token = Sub; return; }    // -
        else if (token == '*') { token = Mul; return; }    // *
        else if (token == '.') { token = Dot; return; }    // .
        else if (token == ':') { token = Colon; return; }  // :
        // 处理分隔符（直接返回ASCII值）
        else if (token == '(' || token == ')' || token == '{' || token == '}' ||
                 token == ',' || token == ';' || token == '[' || token == ']') {
            return;
        }
    }
}

/* ========== 语法分析器辅助函数 ========== */

/*
 * 代码溢出检查函数
 * 功能：检查代码生成是否会导致代码段溢出
 * 参数：instructions_to_add - 即将添加的指令数量
 */
void check_code_overflow(int instructions_to_add) {
    if (code + instructions_to_add >= code_end) {
        printf("line %d: fatal error: code segment overflow\n", line);
        exit(-1);
    }
}

/*
 * 断言函数
 * 功能：检查当前标记是否与期望的标记匹配
 * 参数：tk - 期望的标记类型
 *
 * 如果匹配，则读取下一个标记
 * 如果不匹配，则报错并退出程序
 */
void assert(l25_int tk) {
    if (token != tk) {
        printf("line %d: error: expected token %ld, but got %ld\n", line, tk, token);
        exit(-1);
    }
    tokenize(); // 读取下一个标记
}

/* ========== L25语法分析器函数 ========== */

// 函数声明：前向声明，因为函数之间存在相互调用
void parse_expr();        // 解析表达式
void parse_stmt();        // 解析语句
void parse_stmt_list();   // 解析语句列表
void parse_struct_def();  // 解析结构体定义
int find_struct_member(l25_int *struct_sym, l25_int *member_sym); // 查找结构体成员

/*
 * 解析因子（Factor）
 * 功能：解析表达式中的基本元素
 *
 * 支持的因子类型：
 * - 标识符（变量、函数调用、数组访问、结构体成员访问）
 * - 数字字面量
 * - 括号表达式
 */
void parse_factor() {
    l25_int *id_sym_entry; // 标识符的符号表条目

    if (token == Id) {
        id_sym_entry = symbol_ptr; // 保存当前标识符的符号表条目
        tokenize();

        if (token == '(') {
            // 函数调用：identifier(arg1, arg2, ...)
            assert('(');
            int arg_count = 0; // 参数计数器

            if (token != ')') {
                // 解析参数列表
                while (1) {
                    parse_expr();                    // 解析参数表达式
                    check_code_overflow(1);
                    *++code = PUSH;                  // 将参数压栈
                    arg_count++;
                    if (token == ',') assert(',');   // 处理参数分隔符
                    else break;
                }
            }
            assert(')');

            // 生成函数调用指令
            check_code_overflow(4);
            *++code = CALL;
            *++code = id_sym_entry[Value];           // 函数地址
            if (arg_count > 0) {
                *++code = DARG;
                *++code = arg_count;                 // 清理参数
            }
        }
        else if (token == '[') {
            // 数组访问：identifier[index]
            assert('[');
            parse_expr();                            // 解析索引表达式
            assert(']');

            // 生成数组访问指令
            check_code_overflow(5);
            *++code = PUSH;                          // 保存索引
            *++code = LEA;
            *++code = id_sym_entry[Value];           // 数组基址
            *++code = LEA_ARRAY;                     // 计算元素地址
            *++code = LI;                            // 加载元素值
        }
        else if (token == Dot) {
            // 结构体成员访问：identifier.member
            assert(Dot);
            assert(Id);
            int member_offset = find_struct_member((l25_int*)id_sym_entry[Extra], symbol_ptr);

            // 生成结构体成员访问指令
            check_code_overflow(5);
            *++code = LEA;
            *++code = id_sym_entry[Value];           // 结构体基址
            *++code = LEA_STRUCT;
            *++code = member_offset;                 // 成员偏移量
            *++code = LI;                            // 加载成员值
        }
        else {
            // 简单变量访问：identifier
            check_code_overflow(3);
            *++code = LEA;
            *++code = id_sym_entry[Value];           // 变量地址
            *++code = LI;                            // 加载变量值
        }
    }
    else if (token == Num) {
        // 数字字面量
        check_code_overflow(2);
        *++code = IMM;
        *++code = token_val;                         // 立即数值
        tokenize();
    }
    else if (token == '(') {
        // 括号表达式：(expression)
        assert('(');
        parse_expr();                                // 解析括号内的表达式
        assert(')');
    }
}

/*
 * 解析项（Term）
 * 功能：处理乘法和除法运算
 *
 * 语法：term = factor { ('*' | '/') factor }
 */
void parse_term() {
    parse_factor(); // 解析第一个因子

    // 处理连续的乘法和除法运算（左结合）
    while (token == Mul || token == Div) {
        int op = token;     // 保存运算符
        tokenize();

        // 生成运算指令
        check_code_overflow(2);
        *++code = PUSH;     // 保存左操作数
        parse_factor();     // 解析右操作数
        *++code = (op == Mul) ? MUL : DIV; // 生成对应的运算指令
    }
}

/*
 * 解析加减表达式（Sum Expression）
 * 功能：处理加法、减法运算和一元正负号
 *
 * 语法：sum_expr = ['+' | '-'] term { ('+' | '-') term }
 */
void parse_sum_expr()
{
    int unary_op = 0; // 一元操作符标志：0=无，1=正号，2=负号

    // 处理一元正负号
    if (token == Add)
    {
        unary_op = 1; // 一元正号（实际上不需要特殊处理）
        tokenize();
    }
    else if (token == Sub)
    {
        unary_op = 2; // 一元负号
        tokenize();
    }

    // 如果是一元负号，先压入0作为被减数
    if (unary_op == 2) {
        check_code_overflow(3);
        *++code = IMM; *++code = 0;  // 压入常数0
        *++code = PUSH;              // 将0压栈
    }

    parse_term(); // 解析第一个项

    // 如果是一元负号，执行0减去该项的操作
    if (unary_op == 2) {
        check_code_overflow(1);
        *++code = SUB; // 执行 0 - term
    }

    // 处理连续的加法和减法运算（左结合）
    while (token == Add || token == Sub)
    {
        int op = token;     // 保存运算符
        tokenize();

        // 生成运算指令
        check_code_overflow(2);
        *++code = PUSH;     // 保存左操作数
        parse_term();       // 注意：这里不是parse_sum_expr()，以避免递归问题
        *++code = (op == Add) ? ADD : SUB; // 生成对应的运算指令
    }
}

/*
 * 解析表达式（Expression）
 * 功能：处理比较运算符
 *
 * 语法：expr = sum_expr [ ('==' | '!=' | '<' | '<=' | '>' | '>=') sum_expr ]
 */
void parse_expr() {
    parse_sum_expr(); // 解析左操作数

    // 处理比较运算符（非结合性）
    if (token == Eq || token == Ne || token == Lt || token == Le || token == Gt || token == Ge) {
        int op = token;   // 保存比较运算符
        tokenize();

        // 生成比较指令
        check_code_overflow(2);
        *++code = PUSH;   // 保存左操作数
        parse_sum_expr(); // 解析右操作数（使用parse_sum_expr()避免右结合性）

        // 根据运算符生成对应的比较指令
        switch(op) {
            case Eq: *++code = EQ; break;  // ==
            case Ne: *++code = NE; break;  // !=
            case Lt: *++code = LT; break;  // <
            case Le: *++code = LE; break;  // <=
            case Gt: *++code = GT; break;  // >
            case Ge: *++code = GE; break;  // >=
        }
    }
}

/*
 * 解析变量声明语句
 * 功能：处理let语句的变量声明
 *
 * 支持的声明类型：
 * - 简单整数变量：let var
 * - 结构体变量：let obj : StructType
 * - 数组变量：let arr[size]
 * - 带初始化的声明：let var = expr
 */
void parse_declare_stmt() {
    assert(Let);    // 确认let关键字
    assert(Id);     // 确认变量名
    l25_int *var_to_declare = symbol_ptr; // 获取变量的符号表条目

    var_to_declare[Class] = Loc; // 设置为局部变量

    if (token == Colon) {
        // 结构体变量声明：let obj : StructType
        assert(Colon);
        assert(Id);
        l25_int *type_sym = symbol_ptr;                        // 获取结构体类型符号
        var_to_declare[Type] = STRUCT_TYPE;                    // 设置类型为结构体
        var_to_declare[Extra] = (l25_int)(uintptr_t)type_sym;  // 存储类型信息
        current_func_var_count += type_sym[Extra];             // 为所有成员分配栈空间
    }
    else if (token == '[') {
        // 数组声明：let arr[size]
        assert('[');
        assert(Num);
        l25_int array_size = token_val;                        // 获取数组大小
        assert(']');
        var_to_declare[Type] = ARRAY_TYPE;                     // 设置类型为数组
        var_to_declare[Extra] = INT_TYPE;                      // 数组元素类型为整数
        current_func_var_count += array_size;                 // 为数组元素分配栈空间
    }
    else {
        // 简单整数变量：let var
        var_to_declare[Type] = INT_TYPE;                       // 设置类型为整数
        current_func_var_count++;                              // 分配一个栈位置
    }
    var_to_declare[Value] = -current_func_var_count;           // 设置变量的栈偏移（负数表示局部变量）

    // 处理可选的初始化
    if (token == Assign) {
        assert(Assign);

        // 生成初始化代码
        check_code_overflow(3);
        *++code = LEA;
        *++code = var_to_declare[Value]; // 变量地址
        *++code = PUSH;                  // 将地址压栈
        parse_expr();                    // 解析初始化表达式
        check_code_overflow(1);
        *++code = SI;                    // 存储初始化值
    }
}

/*
 * 解析赋值语句体
 * 功能：处理不同类型的赋值操作
 * 参数：id_var_entry - 被赋值变量的符号表条目
 *
 * 支持的赋值类型：
 * - 数组元素赋值：arr[index] = expr
 * - 结构体成员赋值：obj.member = expr
 * - 简单变量赋值：var = expr
 */
void parse_assign_stmt_body(l25_int *id_var_entry) {
    if (token == '[') {
        // 数组元素赋值：arr[index] = expr
        assert('[');
        parse_expr();    // 解析索引表达式
        assert(']');
        assert(Assign);

        // 生成数组元素赋值代码
        check_code_overflow(5);
        *++code = PUSH;                      // 保存索引值
        *++code = LEA;
        *++code = id_var_entry[Value];       // 数组基址
        *++code = LEA_ARRAY;                 // 计算元素地址
        *++code = PUSH;                      // 将元素地址压栈
        parse_expr();                        // 解析赋值表达式
        check_code_overflow(1);
        *++code = SI;                        // 存储到数组元素
    }
    else if (token == Dot) {
        // 结构体成员赋值：obj.member = expr
        assert(Dot);
        assert(Id);
        int member_offset = find_struct_member((l25_int *)id_var_entry[Extra], symbol_ptr);
        assert(Assign);

        // 生成结构体成员赋值代码
        check_code_overflow(5);
        *++code = LEA;
        *++code = id_var_entry[Value];       // 结构体基址
        *++code = LEA_STRUCT;
        *++code = member_offset;             // 成员偏移量
        *++code = PUSH;                      // 将成员地址压栈
        parse_expr();                        // 解析赋值表达式
        check_code_overflow(1);
        *++code = SI;                        // 存储到结构体成员
    }
    else if (token == Assign) {
        // 简单变量赋值：var = expr
        assert(Assign);

        // 生成简单赋值代码
        check_code_overflow(3);
        *++code = LEA;
        *++code = id_var_entry[Value];       // 变量地址
        *++code = PUSH;                      // 将地址压栈
        parse_expr();                        // 解析赋值表达式
        check_code_overflow(1);
        *++code = SI;                        // 存储到变量
    }
}

/*
 * 解析函数调用语句体
 * 功能：处理函数调用的参数解析和代码生成
 * 参数：func_id_entry - 函数的符号表条目
 */
void parse_func_call_stmt_body(l25_int *func_id_entry) {
    assert('(');
    int arg_count = 0; // 参数计数器

    if (token != ')') {
        // 解析参数列表
        while (1) {
            parse_expr();                    // 解析参数表达式
            check_code_overflow(1);
            *++code = PUSH;                  // 将参数压栈
            arg_count++;
            if (token == ',') assert(',');   // 处理参数分隔符
            else break;
        }
    }
    assert(')');

    // 生成函数调用代码
    check_code_overflow(4);
    *++code = CALL;
    *++code = func_id_entry[Value];          // 函数地址
    if (arg_count > 0) {
        *++code = DARG;
        *++code = arg_count;                 // 清理参数
    }
}

/*
 * 解析if语句
 * 功能：处理条件分支语句
 *
 * 语法：if (condition) { statements } [else { statements }]
 */
void parse_if_stmt() {
    assert(If);     // 确认if关键字
    assert('(');    // 确认左括号
    parse_expr();   // 解析条件表达式
    assert(')');    // 确认右括号

    // 生成条件跳转指令
    check_code_overflow(2);
    *++code = JZ;                                    // 如果条件为假则跳转
    l25_int *patch_jz = ++code;                      // 保存跳转地址的位置（待回填）

    // 解析if分支
    assert('{');
    parse_stmt_list();
    assert('}');

    if (token == Else) {
        // 处理else分支
        assert(Else);

        // 生成跳过else分支的跳转指令
        check_code_overflow(2);
        *++code = JMP;                               // 无条件跳转到if语句结束
        l25_int *patch_jmp_endif = ++code;           // 保存跳转地址的位置（待回填）

        // 回填if条件为假时的跳转地址（跳转到else分支）
        *patch_jz = (l25_int)(uintptr_t)(code + 1);

        // 解析else分支
        assert('{');
        parse_stmt_list();
        assert('}');

        // 回填跳过else分支的跳转地址
        *patch_jmp_endif = (l25_int)(uintptr_t)(code + 1);
    } else {
        // 没有else分支，直接回填条件跳转地址
        *patch_jz = (l25_int)(uintptr_t)(code + 1);
    }
}

/*
 * 解析while循环语句
 * 功能：处理循环语句
 *
 * 语法：while (condition) { statements }
 */
void parse_while_stmt() {
    assert(While);                                   // 确认while关键字
    l25_int *loop_start = code + 1;                  // 记录循环开始位置

    assert('(');
    parse_expr();                                    // 解析循环条件
    assert(')');

    // 生成条件跳转指令
    check_code_overflow(2);
    *++code = JZ;                                    // 如果条件为假则跳出循环
    l25_int *patch_jz_end = ++code;                  // 保存跳转地址的位置（待回填）

    // 解析循环体
    assert('{');
    parse_stmt_list();
    assert('}');

    // 生成回到循环开始的跳转指令
    check_code_overflow(2);
    *++code = JMP;
    *++code = (l25_int)(uintptr_t)loop_start;        // 跳转到循环开始

    // 回填跳出循环的地址
    *patch_jz_end = (l25_int)(uintptr_t)(code + 1);
}

/*
 * 解析输入语句
 * 功能：处理input语句，从标准输入读取数据
 *
 * 语法：input(var1, var2, ...)
 */
void parse_input_stmt() {
    assert(Input);  // 确认input关键字
    assert('(');    // 确认左括号

    int first = 1;  // 标记是否为第一个变量
    while (1) {
        if (!first) assert(',');  // 非第一个变量需要逗号分隔
        first = 0;
        assert(Id);               // 确认变量名

        // 生成输入代码
        check_code_overflow(5);
        *++code = LEA;
        *++code = symbol_ptr[Value];  // 变量地址
        *++code = PUSH;               // 将地址压栈
        *++code = READ_INT_AX;        // 读取整数到ax
        *++code = SI;                 // 存储到变量

        if (token != ',') break;      // 没有更多变量则退出
    }
    assert(')');    // 确认右括号
}

/*
 * 解析输出语句
 * 功能：处理output语句，向标准输出打印数据
 *
 * 语法：output(expr1, expr2, ...)
 */
void parse_output_stmt() {
    assert(Output); // 确认output关键字
    assert('(');    // 确认左括号

    int first = 1;  // 标记是否为第一个表达式
    while (1) {
        if (!first) assert(',');  // 非第一个表达式需要逗号分隔
        first = 0;
        parse_expr();             // 解析要输出的表达式

        // 生成输出代码
        check_code_overflow(1);
        *++code = PRINT_INT_AX;   // 打印ax中的整数

        if (token != ',') break;  // 没有更多表达式则退出
    }
    assert(')');    // 确认右括号
}

/*
 * 解析语句
 * 功能：根据当前标记类型分发到相应的语句解析函数
 *
 * 支持的语句类型：
 * - let语句：变量声明
 * - 赋值语句：变量、数组元素、结构体成员赋值
 * - 函数调用语句
 * - if语句：条件分支
 * - while语句：循环
 * - input语句：输入
 * - output语句：输出
 */
void parse_stmt() {
    if (token == Let) {
        parse_declare_stmt();                        // 解析变量声明语句
    }
    else if (token == Id) {
        l25_int *id_entry = symbol_ptr;              // 保存标识符的符号表条目
        tokenize();

        if (token == Assign || token == '[' || token == Dot) {
            parse_assign_stmt_body(id_entry);        // 解析赋值语句
        }
        else if (token == '(') {
            parse_func_call_stmt_body(id_entry);     // 解析函数调用语句
        }
        else {
            printf("line %d: error: unexpected token '%ld' after identifier.\n", line, token);
            exit(-1);
        }
    }
    else if (token == If) parse_if_stmt();           // 解析if语句
    else if (token == While) parse_while_stmt();     // 解析while语句
    else if (token == Input) parse_input_stmt();     // 解析input语句
    else if (token == Output) parse_output_stmt();   // 解析output语句
    else if (token == Return) {
        // return语句应该在函数体的末尾，这里出现是错误的
        printf("line %d: error: 'return' statement should be at the end of function body.\n", line);
        exit(-1);
    }
}

/*
 * 解析语句列表
 * 功能：解析一系列语句，直到遇到结束条件
 *
 * 结束条件：
 * - 遇到右大括号 '}'
 * - 在函数体中遇到return语句
 * - 遇到文件结束
 */
void parse_stmt_list() {
    while (token != '}' && !(current_parsing_func_body && token == Return) && token != 0) {
        parse_stmt();   // 解析单个语句
        assert(';');    // 每个语句必须以分号结束
    }
}

/*
 * 解析参数列表
 * 功能：解析函数定义中的参数列表
 * 返回值：参数的数量
 *
 * 语法：(param1, param2, ...)
 */
int parse_param_list() {
    int param_count = 0;  // 参数计数器

    while (token != ')') {
        if (param_count > 0) assert(',');  // 非第一个参数需要逗号分隔
        assert(Id);                        // 确认参数名

        // 保存参数的符号表条目
        param_sym_entries[param_count++] = symbol_ptr;
        if (token != ',') break;           // 没有更多参数则退出
    }

    // 设置参数的符号表信息
    for (int i = 0; i < param_count; ++i) {
        param_sym_entries[i][Class] = Loc;                    // 参数是局部变量
        param_sym_entries[i][Type]  = INT_TYPE;               // 参数类型为整数
        param_sym_entries[i][Value] = (param_count - i) + 1; // 参数的栈偏移（正数）
    }

    return param_count;
}

/*
 * 解析函数定义
 * 功能：解析函数的完整定义
 *
 * 语法：func function_name(param1, param2, ...) { statements return expr; }
 */
void parse_func_def() {
    assert(FuncKw);  // 确认func关键字
    assert(Id);      // 确认函数名

    // 设置函数的符号表信息
    symbol_ptr[Class] = Fun;                              // 标记为函数
    symbol_ptr[Type] = INT_TYPE;                          // 函数返回类型为整数
    symbol_ptr[Value] = (l25_int)(uintptr_t)(code + 1);   // 函数入口地址

    current_func_var_count = 0;  // 重置局部变量计数器

    // 解析参数列表
    assert('(');
    if (token != ')') parse_param_list();  // 如果有参数则解析参数列表
    assert(')');

    // 解析函数体
    assert('{');
    current_parsing_func_body = 1;  // 标记正在解析函数体

    // 生成局部变量分配指令（地址待回填）
    check_code_overflow(2);
    *++code = NVAR;                         // 分配局部变量空间指令
    l25_int *nvar_patch_addr = ++code;      // 保存待回填的地址

    parse_stmt_list();                      // 解析函数体语句
    *nvar_patch_addr = current_func_var_count; // 回填局部变量数量

    // 解析return语句
    assert(Return);
    parse_expr();    // 解析返回值表达式
    assert(';');
    check_code_overflow(1);
    *++code = RET;   // 生成返回指令

    assert('}');
    current_parsing_func_body = 0;  // 结束函数体解析
}

/*
 * 解析结构体定义
 * 功能：解析结构体的定义
 *
 * 语法：struct struct_name { member1; member2; ... }
 */
void parse_struct_def() {
    assert(StructKw);  // 确认struct关键字
    assert(Id);        // 确认结构体名

    // 设置结构体的符号表信息
    l25_int *struct_sym = symbol_ptr;
    struct_sym[Class] = Struct;           // 标记为结构体
    struct_sym[Type]  = STRUCT_TYPE;      // 类型为结构体
    struct_sym[Value] = struct_count;     // 结构体索引

    // 初始化结构体成员数组
    int current_struct_index = struct_count++;
    for (int i = 0; i < MAX_STRUCT_MEMBERS; ++i) {
        struct_members[current_struct_index][i] = NULL;
    }

    // 解析结构体成员
    assert('{');
    int member_count = 0;  // 成员计数器
    while (token != '}' && token != 0) {
        assert(Id);  // 确认成员名

        // 存储成员符号，但不修改其全局条目
        // 偏移量隐式地是其在成员数组中的索引
        struct_members[current_struct_index][member_count++] = symbol_ptr;
        assert(';');  // 每个成员声明以分号结束
    }
    assert('}');

    struct_sym[Extra] = member_count;  // 存储成员数量
}

/*
 * 查找结构体成员
 * 功能：在指定结构体中查找指定成员的偏移量
 * 参数：struct_def_sym - 结构体定义的符号表条目
 *       member_sym - 成员的符号表条目
 * 返回值：成员在结构体中的偏移量（索引）
 */
int find_struct_member(l25_int *struct_def_sym, l25_int *member_sym) {
    int struct_index = struct_def_sym[Value];  // 获取结构体索引
    int member_count = struct_def_sym[Extra];  // 获取成员数量

    // 在成员数组中查找指定成员
    for (int i = 0; i < member_count; ++i) {
        if (struct_members[struct_index][i] == member_sym) {
            return i;  // 返回成员偏移量
        }
    }
    return 0;  // 默认返回第一个成员（错误处理）
}

/*
 * 主解析函数
 * 功能：解析整个L25程序
 *
 * L25程序结构：
 * program program_name {
 *     [struct definitions...]
 *     [function definitions...]
 *     main {
 *         [statements...]
 *     }
 * }
 */
void parse() {
    line = 1; token = 1;  // 初始化行号和标记
    tokenize();           // 读取第一个标记

    // 解析程序头部
    assert(Program);      // 确认program关键字
    assert(Id);           // 确认程序名
    assert('{');          // 确认左大括号

    // 解析结构体和函数定义
    while (token == StructKw || token == FuncKw) {
        if (token == StructKw) {
            parse_struct_def();  // 解析结构体定义
        } else {
            parse_func_def();    // 解析函数定义
        }
    }

    // 解析main块
    assert(MainKw);                              // 确认main关键字
    entry_main_addr = code + 1;                  // 记录main块入口地址
    current_func_var_count = 0;                  // 重置局部变量计数器

    assert('{');
    current_parsing_func_body = 0;               // main块不是函数体

    // 生成main块的局部变量分配指令
    check_code_overflow(2);
    *++code = NVAR;                              // 分配局部变量空间指令
    l25_int *main_nvar_patch_addr = ++code;      // 保存待回填的地址

    parse_stmt_list();                           // 解析main块中的语句
    *main_nvar_patch_addr = current_func_var_count; // 回填局部变量数量

    assert('}');  // main块结束
    assert('}');  // 程序结束

    // 生成程序退出代码
    check_code_overflow(4);
    *++code = IMM; *++code = 0;   // 退出码为0
    *++code = PUSH; *++code = EXIT; // 程序退出
}

/* ========== 关键字初始化和虚拟机初始化 ========== */

/*
 * 关键字初始化函数
 * 功能：将L25语言的关键字添加到符号表中
 */
void keyword() {
    int i;
    // L25语言的关键字列表
    char *keywords_str[] = {"program", "func", "main", "let", "if", "else",
                           "return", "while", "input", "output", "struct", NULL};
    // 对应的标记类型
    int keyword_tokens[] = {Program, FuncKw, MainKw, Let, If, Else,
                           Return, While, Input, Output, StructKw};

    char *saved_src = src;  // 保存当前源代码指针

    // 遍历关键字列表，将每个关键字添加到符号表
    for (i = 0; keywords_str[i]; ++i) {
        src = keywords_str[i];           // 设置源代码指针到关键字
        tokenize();                      // 词法分析，将关键字添加到符号表
        symbol_ptr[Token] = keyword_tokens[i]; // 设置关键字的标记类型
    }

    src = saved_src;  // 恢复源代码指针
}

/*
 * 虚拟机初始化函数
 * 功能：分配和初始化虚拟机的内存段
 * 返回值：0表示成功
 */
int init_vm() {
    // 分配内存段
    code = code_dump = malloc(MAX_SIZE);     // 代码段
    stack = malloc(MAX_SIZE);                // 栈段
    symbol_table = malloc(MAX_SIZE / 8);     // 符号表

    // 清零内存
    memset(code, 0, MAX_SIZE);
    memset(stack, 0, MAX_SIZE);
    memset(symbol_table, 0, MAX_SIZE / 8);

    // 设置边界指针
    code_end = code + MAX_SIZE / sizeof(l25_int);           // 代码段结束位置
    stack_start = stack;                                    // 栈段起始位置
    symbol_table_end = symbol_table + (MAX_SIZE / 8) / sizeof(l25_int); // 符号表结束位置

    return 0;
}

/* ========== 虚拟机执行引擎 ========== */

/*
 * 虚拟机运行函数
 * 功能：执行编译生成的虚拟机指令
 * 参数：argc_param - 命令行参数数量（未使用）
 *       argv_param - 命令行参数数组（未使用）
 * 返回值：程序退出码
 */
int run_vm(int argc_param, char **argv_param) {
    l25_int op;  // 当前执行的指令

    // 初始化虚拟机寄存器
    bp = sp = (l25_int *)((uintptr_t)stack + MAX_SIZE);  // 栈指针和基址指针指向栈顶

    // 检查main块入口地址是否已定义
    if (!entry_main_addr) {
        printf("L25 main block entry point not defined.\n");
        exit(-1);
    }

    pc = entry_main_addr;  // 程序计数器指向main块入口
    cycle = 0;             // 初始化指令执行计数器

    // 主执行循环
    while (1) {
        cycle++;           // 指令计数器递增
        op = *pc++;        // 取指令并递增程序计数器

        // 指令解码和执行
        if (op == IMM) {
            // 立即数加载：将下一个字加载到ax寄存器
            ax = *pc++;
        }
        else if (op == LEA) {
            // 加载有效地址：计算bp + offset的地址
            ax = (l25_int)(uintptr_t)(bp + *pc++);
        }
        else if (op == JMP) {
            // 无条件跳转：跳转到指定地址
            pc = (l25_int *)(uintptr_t)*pc;
        }
        else if (op == JZ) {
            // 零跳转：如果ax为0则跳转，否则继续
            pc = ax ? pc + 1 : (l25_int *)(uintptr_t)*pc;
        }
        else if (op == JNZ) {
            // 非零跳转：如果ax非0则跳转，否则继续
            pc = ax ? (l25_int *)(uintptr_t)*pc : pc + 1;
        }
        else if (op == CALL) {
            // 函数调用：保存返回地址并跳转到函数
            *--sp = (l25_int)(uintptr_t)(pc + 1);  // 压入返回地址
            pc = (l25_int *)(uintptr_t)*pc;        // 跳转到函数地址
        }
        else if (op == NVAR) {
            // 新建变量空间：为局部变量分配栈空间
            *--sp = (l25_int)(uintptr_t)bp;  // 保存旧的基址指针
            bp = sp;                         // 设置新的基址指针
            sp = sp - *pc++;                 // 分配局部变量空间
        }
        else if (op == DARG) {
            // 删除参数：清理函数参数占用的栈空间
            sp = sp + *pc++;
        }
        else if (op == RET) {
            // 函数返回：恢复栈状态并返回到调用点
            sp = bp;                                    // 恢复栈指针
            bp = (l25_int *)(uintptr_t)*sp++;          // 恢复基址指针
            pc = (l25_int *)(uintptr_t)*sp++;          // 恢复程序计数器
        }
        else if (op == LI) {
            // 加载间接：从ax指向的地址加载值到ax
            ax = *(l25_int *)(uintptr_t)ax;
        }
        else if (op == SI) {
            // 存储间接：将ax的值存储到栈顶地址指向的位置
            *(l25_int *)(uintptr_t)*sp++ = ax;
        }
        else if (op == PUSH) {
            // 压栈：将ax的值压入栈
            *--sp = ax;
        }
        // 算术运算指令
        else if (op == ADD) { ax = *sp++ + ax; }   // 加法
        else if (op == SUB) { ax = *sp++ - ax; }   // 减法
        else if (op == MUL) { ax = *sp++ * ax; }   // 乘法
        else if (op == DIV) { ax = *sp++ / ax; }   // 除法
        // 比较运算指令
        else if (op == EQ) { ax = (*sp++ == ax); } // 等于
        else if (op == NE) { ax = (*sp++ != ax); } // 不等于
        else if (op == LT) { ax = (*sp++ < ax); }  // 小于
        else if (op == GT) { ax = (*sp++ > ax); }  // 大于
        else if (op == LE) { ax = (*sp++ <= ax); } // 小于等于
        else if (op == GE) { ax = (*sp++ >= ax); } // 大于等于
        else if (op == EXIT) {
            // 程序退出：打印退出信息并返回退出码
            printf("L25 program exited with code: %ld (cycles: %ld)\n", *sp, cycle);
            return *sp;
        }
        else if (op == READ_INT_AX) {
            // 读取整数：从标准输入读取整数到ax
            fflush(stdout);
            scanf("%ld", &ax);
        }
        else if (op == PRINT_INT_AX) {
            // 打印整数：将ax中的整数打印到标准输出
            printf("%ld\n", ax);
            fflush(stdout);
        }
        else if (op == LEA_ARRAY) {
            // 数组元素地址计算：计算数组元素的地址
            ax = (l25_int)((uintptr_t)ax + (*sp++) * sizeof(l25_int));
        }
        else if (op == LEA_STRUCT) {
            // 结构体成员地址计算：计算结构体成员的地址
            ax = (l25_int)((uintptr_t)ax + (*pc++) * sizeof(l25_int));
        }
        else {
            // 未知指令：返回错误码
            return -1;
        }
    }
    return 0;
}



/* ========== 调试和主函数 ========== */

/*
 * 汇编代码输出函数
 * 功能：将生成的虚拟机指令以汇编格式输出到文件
 *
 * 输出格式：地址: 指令助记符 [操作数]
 * 输出文件：assemble.l25
 */
void write_as() {
    int fd;
    char buffer[100];
    // 指令助记符字符串（每个助记符占5个字符）
    char *insts = "IMM ,LEA ,JMP ,JZ  ,JNZ ,CALL,NVAR,DARG,RET ,LI  ,SI  ,PUSH,ADD ,SUB ,MUL ,DIV ,EQ  ,NE  ,LT  ,GT  ,LE  ,GE  ,EXIT,RDAX,PTAX,LEAR,LEAS,";

    // 创建输出文件
    if ((fd = open("assemble.l25", O_WRONLY | O_CREAT | O_TRUNC, 0644)) < 0) {
        printf("Failed to open assemble.l25.\n");
        return;
    }

    l25_int *ptr = code_dump;  // 指向代码段开始
    int addr = 0;              // 地址计数器

    // 遍历所有生成的指令
    while(ptr <= code) {
        l25_int op = *ptr;     // 当前指令
        char mnemonic[6];      // 助记符缓冲区

        // 根据指令码获取助记符
        if (op >= 0 && (op * 5 + 4) < strlen(insts)) {
            strncpy(mnemonic, insts + (op * 5), 4);  // 提取4字符助记符
            mnemonic[4] = '\0';
        } else {
            strcpy(mnemonic, "UNKN");  // 未知指令
        }

        // 输出地址和助记符
        snprintf(buffer, sizeof(buffer), "%04d: %-5s", addr, mnemonic);
        write(fd, buffer, strlen(buffer));

        ptr++; addr++;

        // 检查是否需要输出操作数
        if (op == IMM || op == LEA || op == JZ || op == JNZ || op == CALL ||
            op == NVAR || op == DARG || op == JMP || op == LEA_STRUCT) {
            snprintf(buffer, sizeof(buffer), " %ld\n", *ptr);  // 输出操作数
            ptr++; addr++;
        } else {
            strcpy(buffer, "\n");  // 只输出换行
        }
        write(fd, buffer, strlen(buffer));
    }
    close(fd);
    printf("Assembly-like output written to assemble.l25\n");
}

/*
 * 源代码加载函数
 * 功能：从文件中加载L25源代码
 * 参数：file - 源代码文件名
 * 返回值：0表示成功
 */
int load_src(char *file) {
    int fd = open(file, O_RDONLY);  // 以只读方式打开文件
    int cnt;

    src = src_dump = malloc(MAX_SIZE);  // 分配源代码缓冲区
    cnt = read(fd, src, MAX_SIZE - 1);  // 读取文件内容
    src[cnt] = 0;                       // 添加字符串结束符
    close(fd);
    return 0;
}

/*
 * 主函数
 * 功能：L25编译器和虚拟机的入口点
 * 参数：argc - 命令行参数数量
 *       argv - 命令行参数数组
 * 返回值：程序退出码
 *
 * 执行流程：
 * 1. 加载源代码
 * 2. 初始化虚拟机
 * 3. 初始化关键字
 * 4. 编译源代码
 * 5. 输出汇编代码（调试用）
 * 6. 运行虚拟机
 */
int main(int argc, char *argv[]) {
    MAX_SIZE = 256 * 1024 * 8;  // 设置内存大小为2MB

    load_src(argv[1]);           // 加载源代码文件
    init_vm();                   // 初始化虚拟机
    keyword();                   // 初始化关键字
    code++;                      // 代码生成从地址1开始（避免地址0）
    parse();                     // 编译源代码
    write_as();                  // 输出汇编代码

    return run_vm(argc - 1, argv + 1);  // 运行虚拟机
}