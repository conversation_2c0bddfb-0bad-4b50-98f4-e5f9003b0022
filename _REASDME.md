## 文法定义
**程序结构：**

```
<program> = "program" <ident> "{" { <struct_def> | <func_def> } "main" "{" <stmt_list> "}" "}"
```

**结构体定义：**
```
<struct_def> = "struct" <ident> "{" { <ident> ";" } "}"
```

**函数定义：**
```
<func_def> = "func" <ident> "(" [ <param_list> ] ")" "{" <stmt_list> "return" <expr> ";" "}"
```

**参数列表：**
```
<param_list> = <ident> { "," <ident> }
```

**语句列表：**
```
<stmt_list> = <stmt> ";" { <stmt> ";" }
```

**语句：**
```
<stmt> = <declare_stmt> | <assign_stmt> | <if_stmt> | <while_stmt> | <input_stmt> | <output_stmt> | <func_call>
```

**声明语句（扩展）：**
```
<declare_stmt> = "let" <ident> [ <type_spec> ] [ "=" <expr> ]
<type_spec> = ":" <ident>           // 结构体类型声明
            | "[" <number> "]"      // 数组大小声明
```

**赋值语句（扩展）：**
```
<assign_stmt> = <lvalue> "=" <expr>
<lvalue> = <ident>                  // 简单变量
         | <ident> "[" <expr> "]"   // 数组元素
         | <ident> "." <ident>      // 结构体成员
```

**控制语句：**
```
<if_stmt> = "if" "(" <bool_expr> ")" "{" <stmt_list> "}" [ "else" "{" <stmt_list> "}" ]
<while_stmt> = "while" "(" <bool_expr> ")" "{" <stmt_list> "}"
```

**输入输出语句：**
```
<input_stmt> = "input" "(" <ident> { "," <ident> } ")"
<output_stmt> = "output" "(" <expr> { "," <expr> } ")"
```

**函数调用：**
```
<func_call> = <ident> "(" [ <arg_list> ] ")"
<arg_list> = <expr> { "," <expr> }
```

**布尔表达式：**
```
<bool_expr> = <expr> ("==" | "!=" | "<" | "<=" | ">" | ">=") <expr>
```

**表达式（扩展）：**
```
<expr> = [ "+" | "-" ] <term> { ("+" | "-") <term> }
<term> = <factor> { ("*" | "/") <factor> }
<factor> = <ident>                      // 简单变量
         | <ident> "(" [ <arg_list> ]")" // 函数调用
         | <ident> "[" <expr> "]"       // 数组访问
         | <ident> "." <ident>          // 结构体成员访问
         | <number>                     // 数字字面量
         | "(" <expr> ")"               // 括号表达式
```

**基本元素：**
```
<ident> = <letter> { <letter> | <digit> }
<number> = <digit> { <digit> }
<letter> = "a" | "b" | ... | "z" | "A" | "B" | ... | "Z" | "_"
<digit> = "0" | "1" | ... | "9"
```


```mermaid

graph TD
    A[开始] --> B{读取当前字符};
    B --> C{是换行符?};
    C -- 是 --> D[行号递增line++];
    D --> B;
    C -- 否 --> E{是空白字符?};
    E -- 是 --> B;
    E -- 否 --> F{是字母或下划线?};
    F -- 是 --> G[识别标识符/关键字];
    G --> H{符号表查找};
    H -- 找到 --> I[设置token为对应类型];
    I --> J[返回];
    H -- 未找到 --> K[添加为新Id, 设置token=Id];
    K --> J;
    F -- 否 --> L{是数字?};
    L -- 是 --> M[识别数字字面量];
    M --> N[设置token=Num];
    N --> J;
    L -- 否 --> O{是 '/' ?};
    O -- 是 --> P{下一个字符是 '/' ?};
    P -- 是 --> Q[跳过注释到行尾];
    Q --> B;
    P -- 否 --> R[设置token=Div];
    R --> J;
    O -- 否 --> S{是 '=' ?};
    S -- 是 --> T{下一个字符是 '=' ?};
    T -- 是 --> U[设置token=Eq];
    U --> J;
    T -- 否 --> V[设置token=Assign];
    V --> J;
    S -- 否 --> W{是 '!' ?};
    W -- 是 --> X{下一个字符是 '=' ?};
    X -- 是 --> Y[设置token=Ne];
    Y --> J;
    W -- 否 --> Z{是 '<' ?};
    Z -- 是 --> A1{下一个字符是 '=' ?};
    A1 -- 是 --> B1[设置token=Le];
    B1 --> J;
    A1 -- 否 --> C1[设置token=Lt];
    C1 --> J;
    Z -- 否 --> D1{是 '>' ?};
    D1 -- 是 --> E1{下一个字符是 '=' ?};
    E1 -- 是 --> F1[设置token=Ge];
    F1 --> J;
    E1 -- 否 --> G1[设置token=Gt];
    G1 --> J;
    D1 -- 否 --> H1{"是 '+' '-' '*' '.' ':' '(' ')' '{' '}' ',' ';' '[' ']' ?"};
    H1 -- 是 --> I1[设置token为对应ASCII值];
    I1 --> J;
    H1 -- 否 --> J1{文件结束?};
    J1 -- 是 --> K1[结束];
    %% 忽略未知字符
    J1 -- 否 --> B;

```

## 代码结构

