## 文法定义
**程序结构：**

```
<program> = "program" <ident> "{" { <struct_def> | <func_def> } "main" "{" <stmt_list> "}" "}"
```

**结构体定义：**
```
<struct_def> = "struct" <ident> "{" { <ident> ";" } "}"
```

**函数定义：**
```
<func_def> = "func" <ident> "(" [ <param_list> ] ")" "{" <stmt_list> "return" <expr> ";" "}"
```

**参数列表：**
```
<param_list> = <ident> { "," <ident> }
```

**语句列表：**
```
<stmt_list> = <stmt> ";" { <stmt> ";" }
```

**语句：**
```
<stmt> = <declare_stmt> | <assign_stmt> | <if_stmt> | <while_stmt> | <input_stmt> | <output_stmt> | <func_call>
```

**声明语句（扩展）：**
```
<declare_stmt> = "let" <ident> [ <type_spec> ] [ "=" <expr> ]
<type_spec> = ":" <ident>           // 结构体类型声明
            | "[" <number> "]"      // 数组大小声明
```

**赋值语句（扩展）：**
```
<assign_stmt> = <lvalue> "=" <expr>
<lvalue> = <ident>                  // 简单变量
         | <ident> "[" <expr> "]"   // 数组元素
         | <ident> "." <ident>      // 结构体成员
```

**控制语句：**
```
<if_stmt> = "if" "(" <bool_expr> ")" "{" <stmt_list> "}" [ "else" "{" <stmt_list> "}" ]
<while_stmt> = "while" "(" <bool_expr> ")" "{" <stmt_list> "}"
```

**输入输出语句：**
```
<input_stmt> = "input" "(" <ident> { "," <ident> } ")"
<output_stmt> = "output" "(" <expr> { "," <expr> } ")"
```

**函数调用：**
```
<func_call> = <ident> "(" [ <arg_list> ] ")"
<arg_list> = <expr> { "," <expr> }
```

**布尔表达式：**
```
<bool_expr> = <expr> ("==" | "!=" | "<" | "<=" | ">" | ">=") <expr>
```

**表达式（扩展）：**
```
<expr> = [ "+" | "-" ] <term> { ("+" | "-") <term> }
<term> = <factor> { ("*" | "/") <factor> }
<factor> = <ident>                      // 简单变量
         | <ident> "(" [ <arg_list> ]")" // 函数调用
         | <ident> "[" <expr> "]"       // 数组访问
         | <ident> "." <ident>          // 结构体成员访问
         | <number>                     // 数字字面量
         | "(" <expr> ")"               // 括号表达式
```

**基本元素：**
```
<ident> = <letter> { <letter> | <digit> }
<number> = <digit> { <digit> }
<letter> = "a" | "b" | ... | "z" | "A" | "B" | ... | "Z" | "_"
<digit> = "0" | "1" | ... | "9"
```


```mermaid

graph TD
    A[开始] --> B{读取当前字符};
    B --> C{是换行符?};
    C -- 是 --> D[行号递增line++];
    D --> B;
    C -- 否 --> E{是空白字符?};
    E -- 是 --> B;
    E -- 否 --> F{是字母或下划线?};
    F -- 是 --> G[识别标识符/关键字];
    G --> H{符号表查找};
    H -- 找到 --> I[设置token为对应类型];
    I --> J[返回];
    H -- 未找到 --> K[添加为新Id, 设置token=Id];
    K --> J;
    F -- 否 --> L{是数字?};
    L -- 是 --> M[识别数字字面量];
    M --> N[设置token=Num];
    N --> J;
    L -- 否 --> O{是 '/' ?};
    O -- 是 --> P{下一个字符是 '/' ?};
    P -- 是 --> Q[跳过注释到行尾];
    Q --> B;
    P -- 否 --> R[设置token=Div];
    R --> J;
    O -- 否 --> S{是 '=' ?};
    S -- 是 --> T{下一个字符是 '=' ?};
    T -- 是 --> U[设置token=Eq];
    U --> J;
    T -- 否 --> V[设置token=Assign];
    V --> J;
    S -- 否 --> W{是 '!' ?};
    W -- 是 --> X{下一个字符是 '=' ?};
    X -- 是 --> Y[设置token=Ne];
    Y --> J;
    W -- 否 --> Z{是 '<' ?};
    Z -- 是 --> A1{下一个字符是 '=' ?};
    A1 -- 是 --> B1[设置token=Le];
    B1 --> J;
    A1 -- 否 --> C1[设置token=Lt];
    C1 --> J;
    Z -- 否 --> D1{是 '>' ?};
    D1 -- 是 --> E1{下一个字符是 '=' ?};
    E1 -- 是 --> F1[设置token=Ge];
    F1 --> J;
    E1 -- 否 --> G1[设置token=Gt];
    G1 --> J;
    D1 -- 否 --> H1{"是 '+' '-' '*' '.' ':' '(' ')' '{' '}' ',' ';' '[' ']' ?"};
    H1 -- 是 --> I1[设置token为对应ASCII值];
    I1 --> J;
    H1 -- 否 --> J1{文件结束?};
    J1 -- 是 --> K1[结束];
    %% 忽略未知字符
    J1 -- 否 --> B;

```



## 代码结构

````c
/* 包含：
 * - 词法分析器（Lexer）：将源代码转换为标记流
 * - 语法分析器（Parser）：将标记流转换为虚拟机指令
 * - 虚拟机（VM）：执行生成的指令
 */
````

#### **1. 头文件和类型定义 (第18-30行)**
```bash
- 系统头文件引入
- L25专用64位整数类型定义
```

#### **2. 全局变量定义 (第32-80行)**
分为几个功能组：

````c
/* 虚拟机内存段 */
l25_int *code;       // 代码段：存储编译后的虚拟机指令
l25_int *code_dump;  // 代码段备份：用于调试输出
l25_int *stack;      // 栈段：运行时数据栈

/* 虚拟机寄存器 */
l25_int *pc; // 程序计数器：指向当前执行的指令
l25_int *sp; // 栈指针：指向栈顶
l25_int *bp; // 基址指针：指向当前栈帧的基址
````

#### **3. 枚举定义 (第82-278行)**
- **虚拟机指令集** (88-133行)：定义所有VM指令
- **标记类型** (139-177行)：词法分析器识别的标记
- **符号表字段** (183-192行)：符号表条目结构
- **数据类型** (197-201行)：L25支持的类型

#### **4. 词法分析器 (第280-309行)**

````C
/*
 * 词法分析函数
 * 功能：将源代码字符流转换为标记流
 */
void tokenize() {
    // 处理标识符、数字、运算符、注释等
}
````

#### **5. 语法分析器辅助函数 (第311-340行)**
- `check_code_overflow()` - 代码溢出检查
- `assert()` - 标记匹配断言

#### **6. 语法分析器核心 (第342-1079行)**
按语法结构组织的解析函数：

**表达式解析：**
- `parse_factor()` - 解析因子
- `parse_term()` - 解析项（乘除）
- `parse_sum_expr()` - 解析加减表达式
- `parse_expr()` - 解析完整表达式

**语句解析：**
- `parse_declare_stmt()` - 变量声明
- `parse_assign_stmt_body()` - 赋值语句
- `parse_if_stmt()` - if语句
- `parse_while_stmt()` - while循环
- `parse_input_stmt()` / `parse_output_stmt()` - 输入输出

**高级结构解析：**
- `parse_func_def()` - 函数定义
- `parse_struct_def()` - 结构体定义
- `parse()` - 主解析函数

#### **7. 初始化模块 (第1081-1130行)**
- `keyword()` - 关键字初始化
- `init_vm()` - 虚拟机内存初始化

#### **8. 虚拟机执行引擎 (第1132-1256行)**

````C
/*
 * 虚拟机运行函数
 * 功能：执行编译生成的虚拟机指令
 */
int run_vm(int argc_param, char **argv_param) {
    // 主执行循环：取指令-解码-执行
    while (1) {
        cycle++;
        op = *pc++;
        // 指令解码和执行...
    }
}
````

#### **9. 调试和工具函数 (第1258-1332行)**
- `write_as()` - 汇编代码输出
- `load_src()` - 源代码加载

#### **10. 主函数 (第1334-1360行)**

````C
/*
 * 执行流程：
 * 1. 加载源代码
 * 2. 初始化虚拟机
 * 3. 初始化关键字
 * 4. 编译源代码
 * 5. 输出汇编代码（调试用）
 * 6. 运行虚拟机
 */
int main(int argc, char *argv[]) {
    load_src(argv[1]);
    init_vm();
    keyword();
    code++;
    parse();
    write_as();
    return run_vm(argc - 1, argv + 1);
}
````

**编译阶段：**
```bash
源代码 → 词法分析器 → 标记流 → 语法分析器 → 虚拟机指令
```

**运行阶段：**
```bash
虚拟机指令 → 虚拟机执行引擎 → 程序输出
```



## 运行方式

```bash
clang-format L25.c -i --style=Microsoft && clang L25.c -o L25 && ./L25 test.l25
```



## 测试结果截图